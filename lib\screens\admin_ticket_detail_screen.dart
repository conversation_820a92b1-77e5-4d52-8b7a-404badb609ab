import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../widgets/main_layout.dart';
import '../config/routes.dart';
import '../config/api_config.dart';
import '../models/ticket.dart';
import '../models/user.dart';
import '../services/ticket_service.dart';
import '../services/user_service.dart';
import '../providers/auth_provider.dart';
import '../utils/timezone_utils.dart';

class AdminTicketDetailScreen extends StatefulWidget {
  final String ticketId;

  const AdminTicketDetailScreen({
    super.key,
    required this.ticketId,
  });

  @override
  State<AdminTicketDetailScreen> createState() => _AdminTicketDetailScreenState();
}

class _AdminTicketDetailScreenState extends State<AdminTicketDetailScreen>
    with TickerProviderStateMixin {
  final TicketService _ticketService = TicketService();
  final UserService _userService = UserService();
  final _commentController = TextEditingController();
  final _scrollController = ScrollController();

  Ticket? _ticket;
  List<User> _availableUsers = [];
  bool _isLoading = true;
  bool _isSubmittingComment = false;
  bool _isUpdatingStatus = false;
  bool _isAssigning = false;
  String? _errorMessage;

  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late AnimationController _commentAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _commentAnimation;

  // 批量删除相关
  bool _isSelectionMode = false;
  Set<int> _selectedCommentIds = {};

  // 评论分页相关
  static const int _commentsPerPage = 10;
  int _currentCommentsPage = 1;
  bool _showAllComments = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadTicket();
    _loadAvailableUsers();
  }

  void _initializeAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _commentAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _commentAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _commentAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    _commentAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadTicket() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final ticket = await _ticketService.getTicket(int.parse(widget.ticketId));
      
      if (mounted) {
        setState(() {
          _ticket = ticket;
          _isLoading = false;
        });
        // 启动动画
        _fadeAnimationController.forward();
        _slideAnimationController.forward();
        _commentAnimationController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadAvailableUsers() async {
    try {
      // 获取所有管理员用户用于分配
      final users = await _userService.getUsers(UserQuery(role: 'Admin'));
      setState(() {
        _availableUsers = users.users;
      });
    } catch (e) {
      // 静默处理错误，不影响主要功能
      print('Failed to load users: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: MainLayout(
        currentRoute: '/admin/tickets/${widget.ticketId}',
        title: _ticket?.ticketNumber ?? '工单详情',
        showBackButton: true,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.grey[50]!,
                Colors.white,
              ],
            ),
          ),
          child: _buildBody(),
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTicket,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_ticket == null) {
      return const Center(
        child: Text('工单不存在'),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildAdminTicketHeader(),
                    const SizedBox(height: 16),
                    _buildAdminActions(),
                    const SizedBox(height: 16),
                    _buildTicketStatusTimeline(),
                    const SizedBox(height: 16),
                    _buildTicketDetails(),
                    const SizedBox(height: 16),
                    _buildCommentsSection(),
                    const SizedBox(height: 16),
                    _buildAddCommentSection(),
                    const SizedBox(height: 100), // 底部空间
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminTicketHeader() {
    final priorityColor = _getPriorityColor(_ticket!.priority);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // 优先级指示条
          Container(
            height: 6,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  priorityColor[400]!,
                  priorityColor[600]!,
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
          ),

          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 工单编号和状态
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: priorityColor[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: priorityColor[200]!),
                      ),
                      child: Text(
                        _ticket!.ticketNumber,
                        style: TextStyle(
                          color: priorityColor[700],
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                    const Spacer(),
                    _buildModernStatusChip(_ticket!.status),
                  ],
                ),
                const SizedBox(height: 20),

                // 标题
                Text(
                  _ticket!.title,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                    height: 1.3,
                  ),
                ),
                const SizedBox(height: 16),

                // 用户信息
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: Colors.blue[600],
                        radius: 20,
                        child: Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '提交用户',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              _ticket!.userName,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[800],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // 标签行
                Wrap(
                  spacing: 12,
                  runSpacing: 8,
                  children: [
                    _buildModernPriorityChip(_ticket!.priority),
                    _buildModernCategoryChip(_ticket!.category),
                    if (_ticket!.assignedToName != null)
                      _buildAssigneeChip(_ticket!.assignedToName!),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdminActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.admin_panel_settings,
                color: Colors.orange[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '管理员操作',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 状态更新按钮
          Row(
            children: [
              Expanded(
                child: _buildStatusUpdateButton('InProgress', '我来处理', Colors.blue, Icons.play_arrow),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatusUpdateButton('Resolved', '我已解决', Colors.green, Icons.check_circle),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatusUpdateButton('Closed', '我来关闭', Colors.grey, Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // 当前处理人信息
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _ticket!.assignedToName != null ? Colors.green[50] : Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _ticket!.assignedToName != null ? Colors.green[200]! : Colors.grey[300]!,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.assignment_ind,
                  color: _ticket!.assignedToName != null ? Colors.green[600] : Colors.grey[600],
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '当前处理人',
                        style: TextStyle(
                          fontSize: 12,
                          color: _ticket!.assignedToName != null ? Colors.green[600] : Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        _ticket!.assignedToName ?? '未分配',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _ticket!.assignedToName != null ? Colors.green[800] : Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
                if (_ticket!.assignedToName != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '已分配',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.green[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // 手动分配按钮（可选）
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _isAssigning ? null : _showAssignDialog,
              icon: _isAssigning
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Icon(Icons.person_add),
              label: Text('手动分配给其他管理员'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.orange[600],
                side: BorderSide(color: Colors.orange[600]!),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusUpdateButton(String status, String label, MaterialColor color, IconData icon) {
    final isCurrentStatus = _ticket!.status == status;
    final isDisabled = isCurrentStatus || _isUpdatingStatus;

    return Container(
      height: 44,
      child: ElevatedButton.icon(
        onPressed: isDisabled ? null : () => _updateTicketStatus(status),
        icon: _isUpdatingStatus
            ? SizedBox(
                width: 14,
                height: 14,
                child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
              )
            : Icon(icon, size: 16),
        label: Text(
          label,
          style: TextStyle(fontSize: 12),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: isCurrentStatus ? Colors.grey[400] : color[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8),
        ),
      ),
    );
  }

  // 状态更新方法 - 自动分配给当前管理员
  Future<void> _updateTicketStatus(String status) async {
    setState(() {
      _isUpdatingStatus = true;
    });

    try {
      // 获取当前管理员信息
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser == null) {
        throw Exception('用户信息获取失败');
      }

      // 更新工单状态并自动分配给当前管理员
      final updateData = {
        'status': status,
        'assignedToId': currentUser.id,
      };

      await _ticketService.updateTicket(_ticket!.id, updateData);
      await _loadTicket(); // 重新加载工单数据

      if (mounted) {
        _showAdvancedSuccessAnimation(
          '状态已更新',
          '工单已自动分配给您',
          _getStatusIcon(status),
          _getStatusColor(status),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdatingStatus = false;
        });
      }
    }
  }

  // 显示分配对话框
  void _showAssignDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('分配工单'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('选择要分配的管理员：'),
            const SizedBox(height: 16),
            ..._availableUsers.map((user) => ListTile(
              leading: CircleAvatar(
                child: Text(user.fullName?.substring(0, 1) ?? user.username.substring(0, 1)),
              ),
              title: Text(user.fullName ?? user.username),
              subtitle: Text(user.email),
              onTap: () {
                Navigator.of(context).pop();
                _assignTicket(user.id);
              },
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('取消'),
          ),
        ],
      ),
    );
  }

  // 分配工单
  Future<void> _assignTicket(int userId) async {
    setState(() {
      _isAssigning = true;
    });

    try {
      await _ticketService.assignTicket(_ticket!.id, userId);
      await _loadTicket(); // 重新加载工单数据

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('工单已分配'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('分配失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAssigning = false;
        });
      }
    }
  }

  // 简化的组件方法 - 复用用户端的逻辑
  Widget _buildTicketStatusTimeline() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.timeline, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text('工单进度', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            ],
          ),
          const SizedBox(height: 16),
          Text('创建时间: ${TimezoneUtils.formatDateTime(_ticket!.createdDate)}'),
          if (_ticket!.updatedDate != null)
            Text('更新时间: ${TimezoneUtils.formatDateTime(_ticket!.updatedDate!)}'),
          if (_ticket!.assignedDate != null)
            Text('分配时间: ${TimezoneUtils.formatDateTime(_ticket!.assignedDate!)}'),
          if (_ticket!.resolvedDate != null)
            Text('解决时间: ${TimezoneUtils.formatDateTime(_ticket!.resolvedDate!)}'),
        ],
      ),
    );
  }

  Widget _buildTicketDetails() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('问题描述', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
          const SizedBox(height: 12),
          Text(_ticket!.description, style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildCommentsSection() {
    final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
    final userComments = _ticket!.comments.where((comment) => comment.userId == currentUser?.id).toList();
    final hasUserComments = userComments.isNotEmpty;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 评论标题和操作按钮
          Row(
            children: [
              Row(
                children: [
                  Icon(Icons.comment, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 8),
                  Text('评论记录', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Text(
                      '${_ticket!.comments.length}',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              if (hasUserComments && !_isSelectionMode)
                Row(
                  children: [
                    IconButton(
                      onPressed: _enterSelectionMode,
                      icon: Icon(Icons.edit, size: 20, color: Colors.blue[600]),
                      tooltip: '管理我的评论',
                    ),
                  ],
                ),
              if (_isSelectionMode)
                Row(
                  children: [
                    TextButton(
                      onPressed: _selectAllUserComments,
                      child: Text('全选', style: TextStyle(color: Colors.blue[600])),
                    ),
                    TextButton(
                      onPressed: _exitSelectionMode,
                      child: Text('取消', style: TextStyle(color: Colors.grey[600])),
                    ),
                    if (_selectedCommentIds.isNotEmpty)
                      IconButton(
                        onPressed: _deleteSelectedComments,
                        icon: Icon(Icons.delete, color: Colors.red[600]),
                        tooltip: '删除选中',
                      ),
                  ],
                ),
            ],
          ),
          const SizedBox(height: 16),

          if (_ticket!.comments.isEmpty)
            Text('暂无评论', style: TextStyle(color: Colors.grey[600]))
          else
            _buildCommentsWithPagination(currentUser),
        ],
      ),
    );
  }

  Widget _buildCommentsWithPagination(User? currentUser) {
    final totalComments = _ticket!.comments.length;
    final displayedComments = _showAllComments
        ? _ticket!.comments
        : _ticket!.comments.take(_commentsPerPage).toList();
    final hasMoreComments = totalComments > _commentsPerPage;

    return Column(
      children: [
        // 显示的评论
        ...displayedComments.map((comment) => _buildCommentCard(comment, currentUser)),

        // 加载更多按钮
        if (hasMoreComments && !_showAllComments) ...[
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showAllComments = true;
                });
              },
              icon: Icon(Icons.expand_more, size: 18),
              label: Text('显示全部 ${totalComments} 条评论'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.blue[600],
                side: BorderSide(color: Colors.blue[300]!),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],

        // 收起按钮
        if (_showAllComments && hasMoreComments) ...[
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showAllComments = false;
                });
                // 滚动到评论区域顶部
                Future.delayed(const Duration(milliseconds: 100), () {
                  if (_scrollController.hasClients) {
                    _scrollController.animateTo(
                      _scrollController.offset - 200,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOut,
                    );
                  }
                });
              },
              icon: Icon(Icons.expand_less, size: 18),
              label: Text('收起评论'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.grey[600],
                side: BorderSide(color: Colors.grey[300]!),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCommentCard(TicketComment comment, User? currentUser) {
    final isUserComment = comment.userId == currentUser?.id;
    final isSelected = _selectedCommentIds.contains(comment.id);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _isSelectionMode && isSelected
            ? Colors.blue[100]
            : (isUserComment ? Colors.blue[50] : Colors.grey[100]),
        borderRadius: BorderRadius.circular(12),
        border: _isSelectionMode && isSelected
            ? Border.all(color: Colors.blue[400]!, width: 2)
            : Border.all(
                color: isUserComment ? Colors.blue[200]! : Colors.grey[300]!,
                width: 1,
              ),
        boxShadow: [
          BoxShadow(
            color: (isUserComment ? Colors.blue : Colors.grey).withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // 选择框
              if (_isSelectionMode && isUserComment)
                Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: Checkbox(
                    value: isSelected,
                    onChanged: (selected) => _toggleCommentSelection(comment.id),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),

              // 用户头像
              _buildUserAvatar(comment, isUserComment),
              const SizedBox(width: 8),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          comment.userName,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isUserComment ? Colors.blue[800] : Colors.grey[800],
                          ),
                        ),
                        if (isUserComment)
                          Container(
                            margin: const EdgeInsets.only(left: 8),
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue[100],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '我',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.blue[700],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                      ],
                    ),
                    Text(
                      TimezoneUtils.formatRelativeTime(comment.createdDate),
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
              ),

              // 单个删除按钮
              if (!_isSelectionMode && isUserComment)
                IconButton(
                  onPressed: () => _deleteComment(comment.id),
                  icon: Icon(Icons.delete_outline, size: 18, color: Colors.red[400]),
                  tooltip: '删除',
                ),
            ],
          ),
          const SizedBox(height: 8),
          Padding(
            padding: EdgeInsets.only(left: _isSelectionMode && isUserComment ? 40 : 32),
            child: Text(comment.content),
          ),
        ],
      ),
    );
  }

  Widget _buildAddCommentSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('添加回复', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          TextField(
            controller: _commentController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: '输入回复内容...',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isSubmittingComment ? null : _submitComment,
              child: _isSubmittingComment
                  ? CircularProgressIndicator(color: Colors.white)
                  : Text('发送回复'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _submitComment() async {
    if (_commentController.text.trim().isEmpty) return;

    // 保存当前滚动位置
    final currentScrollOffset = _scrollController.offset;

    setState(() {
      _isSubmittingComment = true;
    });

    try {
      await _ticketService.addComment(_ticket!.id, _commentController.text.trim());
      _commentController.clear();
      await _loadTicket();

      if (mounted) {
        _showSuccessAnimation('回复已发送');

        // 恢复滚动位置，稍微向下滚动一点以显示新评论
        Future.delayed(const Duration(milliseconds: 100), () {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              currentScrollOffset + 50, // 稍微向下滚动显示新评论
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('发送失败: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmittingComment = false;
        });
      }
    }
  }

  Widget _buildUserAvatar(TicketComment comment, bool isUserComment) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: (isUserComment ? Colors.blue : Colors.grey).withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: CircleAvatar(
        radius: 18,
        backgroundColor: Colors.transparent,
        child: ClipOval(
          child: comment.userAvatarUrl != null && comment.userAvatarUrl!.isNotEmpty
              ? Image.network(
                  comment.userAvatarUrl!,
                  width: 36,
                  height: 36,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildDefaultAvatar(comment, isUserComment);
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              isUserComment ? Colors.blue[600]! : Colors.grey[600]!,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                )
              : _buildDefaultAvatar(comment, isUserComment),
        ),
      ),
    );
  }

  Widget _buildDefaultAvatar(TicketComment comment, bool isUserComment) {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isUserComment
              ? [Colors.blue[400]!, Colors.blue[600]!]
              : [Colors.grey[400]!, Colors.grey[600]!],
        ),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          comment.userName.substring(0, 1).toUpperCase(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  // 评论管理方法
  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
      _selectedCommentIds.clear();
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedCommentIds.clear();
    });
  }

  void _toggleCommentSelection(int commentId) {
    setState(() {
      if (_selectedCommentIds.contains(commentId)) {
        _selectedCommentIds.remove(commentId);
      } else {
        _selectedCommentIds.add(commentId);
      }
    });
  }

  void _selectAllUserComments() {
    final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
    final userCommentIds = _ticket!.comments
        .where((comment) => comment.userId == currentUser?.id)
        .map((comment) => comment.id)
        .toSet();

    setState(() {
      _selectedCommentIds = userCommentIds;
    });
  }

  Future<void> _deleteComment(int commentId) async {
    try {
      await _ticketService.deleteComment(commentId);
      await _loadTicket();
      _showSuccessAnimation('评论已删除');
    } catch (e) {
      _showErrorSnackBar('删除失败: $e');
    }
  }

  Future<void> _deleteSelectedComments() async {
    if (_selectedCommentIds.isEmpty) return;

    try {
      final deletedCount = await _ticketService.batchDeleteComments(_selectedCommentIds.toList());
      await _loadTicket();
      _exitSelectionMode();
      _showSuccessAnimation('共删除了 $deletedCount 条评论');
    } catch (e) {
      _showErrorSnackBar('批量删除失败: $e');
    }
  }

  // 辅助方法
  MaterialColor _getPriorityColor(String priority) {
    switch (priority) {
      case 'High':
        return Colors.red;
      case 'Medium':
        return Colors.orange;
      case 'Low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Widget _buildModernStatusChip(String status) {
    final color = _getStatusColor(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color[200]!),
      ),
      child: Text(
        _getDisplayText(status),
        style: TextStyle(
          color: color[700],
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildModernPriorityChip(String priority) {
    final color = _getPriorityColor(priority);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color[200]!),
      ),
      child: Text(
        _getDisplayText(priority),
        style: TextStyle(
          color: color[700],
          fontSize: 11,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildModernCategoryChip(String category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Text(
        _getDisplayText(category),
        style: TextStyle(
          color: Colors.blue[700],
          fontSize: 11,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildAssigneeChip(String assigneeName) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.assignment_ind, size: 12, color: Colors.green[700]),
          const SizedBox(width: 4),
          Text(
            assigneeName,
            style: TextStyle(
              color: Colors.green[700],
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  MaterialColor _getStatusColor(String status) {
    switch (status) {
      case 'Pending':
        return Colors.orange;
      case 'InProgress':
        return Colors.blue;
      case 'Resolved':
        return Colors.green;
      case 'Closed':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getDisplayText(String value) {
    switch (value) {
      case 'Pending': return '待处理';
      case 'InProgress': return '处理中';
      case 'Resolved': return '已解决';
      case 'Closed': return '已关闭';
      case 'Low': return '低';
      case 'Medium': return '中';
      case 'High': return '高';
      case 'Hardware': return '硬件';
      case 'Software': return '软件';
      case 'Network': return '网络';
      case 'Account': return '账户';
      case 'Other': return '其他';
      default: return value;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'InProgress':
        return Icons.play_arrow;
      case 'Resolved':
        return Icons.check_circle;
      case 'Closed':
        return Icons.close;
      default:
        return Icons.info;
    }
  }

  // 高级动画反馈系统
  void _showAdvancedSuccessAnimation(String title, String subtitle, IconData icon, MaterialColor color) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _AdvancedSuccessDialog(
        title: title,
        subtitle: subtitle,
        icon: icon,
        color: color,
      ),
    );

    // 自动关闭对话框
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    });
  }

  void _showSuccessAnimation(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SimpleSuccessDialog(message: message),
    );

    // 自动关闭对话框
    Future.delayed(const Duration(milliseconds: 1200), () {
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

// 高级成功动画对话框
class _AdvancedSuccessDialog extends StatefulWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final MaterialColor color;

  const _AdvancedSuccessDialog({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });

  @override
  State<_AdvancedSuccessDialog> createState() => _AdvancedSuccessDialogState();
}

class _AdvancedSuccessDialogState extends State<_AdvancedSuccessDialog>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late AnimationController _iconController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _iconAnimation;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _iconController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _iconAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _iconController, curve: Curves.bounceOut),
    );

    // 启动动画序列
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 100), () {
      _scaleController.forward();
    });
    Future.delayed(const Duration(milliseconds: 200), () {
      _iconController.forward();
    });
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: widget.color.withOpacity(0.3),
                  spreadRadius: 0,
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 动画图标
                ScaleTransition(
                  scale: _iconAnimation,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: widget.color[50],
                      shape: BoxShape.circle,
                      border: Border.all(color: widget.color[200]!, width: 3),
                    ),
                    child: Icon(
                      widget.icon,
                      size: 40,
                      color: widget.color[600],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // 标题
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: widget.color[800],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // 副标题
                Text(
                  widget.subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// 简单成功动画对话框
class _SimpleSuccessDialog extends StatefulWidget {
  final String message;

  const _SimpleSuccessDialog({required this.message});

  @override
  State<_SimpleSuccessDialog> createState() => _SimpleSuccessDialogState();
}

class _SimpleSuccessDialogState extends State<_SimpleSuccessDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.3),
                  spreadRadius: 0,
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.green[200]!, width: 2),
                  ),
                  child: Icon(
                    Icons.check,
                    size: 30,
                    color: Colors.green[600],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  widget.message,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
