using System.ComponentModel.DataAnnotations;

namespace ITAssetAPI.Models
{
    public enum TicketStatus
    {
        Pending,
        InProgress,
        Resolved,
        Closed
    }

    public enum TicketPriority
    {
        Low,
        Medium,
        High
    }

    public enum TicketCategory
    {
        Hardware,
        Software,
        Network,
        Account,
        Other
    }

    public class Ticket
    {
        public int Id { get; set; }

        [MaxLength(20)]
        public string TicketNumber { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        public string Description { get; set; } = string.Empty;
        
        public TicketStatus Status { get; set; } = TicketStatus.Pending;
        
        public TicketPriority Priority { get; set; } = TicketPriority.Medium;
        
        public TicketCategory Category { get; set; } = TicketCategory.Other;
        
        [Required]
        public int UserId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string UserName { get; set; } = string.Empty;
        
        public int? AssignedToId { get; set; }
        
        [MaxLength(100)]
        public string? AssignedToName { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedDate { get; set; }

        public DateTime? AssignedDate { get; set; }

        public DateTime? ResolvedDate { get; set; }
        
        // Navigation properties
        public User User { get; set; } = null!;
        public User? AssignedTo { get; set; }
        public ICollection<TicketComment> Comments { get; set; } = new List<TicketComment>();
        public ICollection<TicketAttachment> Attachments { get; set; } = new List<TicketAttachment>();
    }

    public class TicketComment
    {
        public int Id { get; set; }

        [Required]
        public int TicketId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(100)]
        public string UserName { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? UserAvatarUrl { get; set; }

        [Required]
        public string Content { get; set; } = string.Empty;

        public bool IsInternal { get; set; } = false;

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public Ticket Ticket { get; set; } = null!;
        public User User { get; set; } = null!;
    }

    public class TicketAttachment
    {
        public int Id { get; set; }
        
        [Required]
        public int TicketId { get; set; }
        
        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;
        
        public long FileSize { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string ContentType { get; set; } = string.Empty;
        
        public DateTime UploadedDate { get; set; } = DateTime.UtcNow;
        
        // Navigation property
        public Ticket Ticket { get; set; } = null!;
    }

    public class TicketViewRecord
    {
        public int Id { get; set; }

        [Required]
        public int TicketId { get; set; }

        [Required]
        public int UserId { get; set; }

        public DateTime LastViewedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public Ticket Ticket { get; set; } = null!;
        public User User { get; set; } = null!;
    }
}
