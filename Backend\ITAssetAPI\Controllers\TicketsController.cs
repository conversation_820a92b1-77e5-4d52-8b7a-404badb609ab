using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ITAssetAPI.Services;
using ITAssetAPI.DTOs;

namespace ITAssetAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TicketsController : ControllerBase
    {
        private readonly TicketService _ticketService;

        public TicketsController(TicketService ticketService)
        {
            _ticketService = ticketService;
        }

        // GET: api/tickets/user
        [HttpGet("user")]
        public async Task<ActionResult<List<TicketListDto>>> GetUserTickets()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized("Invalid user ID");
            }

            var tickets = await _ticketService.GetUserTicketsAsync(userId);
            return Ok(tickets);
        }

        // GET: api/tickets/user/with-unread
        [HttpGet("user/with-unread")]
        public async Task<ActionResult<List<TicketListDto>>> GetUserTicketsWithUnreadStatus()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized("Invalid user ID");
            }

            var userTickets = await _ticketService.GetUserTicketsAsync(userId);
            var ticketsWithUnread = new List<TicketListDto>();

            foreach (var ticket in userTickets)
            {
                var unreadCount = await _ticketService.GetUnreadCommentsCountAsync(ticket.Id, userId);
                ticket.UnreadCommentsCount = unreadCount;
                ticket.HasUnreadComments = unreadCount > 0;
                ticketsWithUnread.Add(ticket);
            }

            return Ok(ticketsWithUnread);
        }

        // GET: api/tickets
        [HttpGet]
        public async Task<ActionResult<List<TicketListDto>>> GetAllTickets()
        {
            // Check if user is admin
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            if (userRole != "Admin")
            {
                return Forbid("Only administrators can view all tickets");
            }

            var tickets = await _ticketService.GetAllTicketsAsync();
            return Ok(tickets);
        }

        // GET: api/tickets/with-unread
        [HttpGet("with-unread")]
        public async Task<ActionResult<List<TicketListDto>>> GetAllTicketsWithUnreadStatus()
        {
            // Check if user is admin
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            if (userRole != "Admin")
            {
                return Forbid("Only administrators can view all tickets");
            }

            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized("Invalid user ID");
            }

            var tickets = await _ticketService.GetTicketsWithUnreadStatusAsync(userId);
            return Ok(tickets);
        }

        // GET: api/tickets/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<TicketDto>> GetTicket(int id)
        {
            var ticket = await _ticketService.GetTicketByIdAsync(id);
            if (ticket == null)
            {
                return NotFound("Ticket not found");
            }

            // Check if user can access this ticket
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

            if (!int.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized("Invalid user ID");
            }

            // Users can only see their own tickets, admins can see all
            if (userRole != "Admin" && ticket.UserId != userId)
            {
                return Forbid("You can only view your own tickets");
            }

            // Mark ticket as viewed by the current user
            await _ticketService.MarkTicketAsViewedAsync(id, userId);

            return Ok(ticket);
        }

        // POST: api/tickets/{id}/mark-viewed
        [HttpPost("{id}/mark-viewed")]
        public async Task<IActionResult> MarkTicketAsViewed(int id)
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized("Invalid user ID");
            }

            // Check if ticket exists and user can access it
            var ticket = await _ticketService.GetTicketByIdAsync(id);
            if (ticket == null)
            {
                return NotFound("Ticket not found");
            }

            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            if (userRole != "Admin" && ticket.UserId != userId)
            {
                return Forbid("You can only mark your own tickets as viewed");
            }

            await _ticketService.MarkTicketAsViewedAsync(id, userId);
            return Ok();
        }

        // POST: api/tickets
        [HttpPost]
        public async Task<ActionResult<TicketDto>> CreateTicket(CreateTicketDto createTicketDto)
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userName = User.FindFirst(ClaimTypes.Name)?.Value;

            if (!int.TryParse(userIdClaim, out var userId) || string.IsNullOrEmpty(userName))
            {
                return Unauthorized("Invalid user information");
            }

            var ticket = await _ticketService.CreateTicketAsync(createTicketDto, userId, userName);
            return CreatedAtAction(nameof(GetTicket), new { id = ticket.Id }, ticket);
        }

        // PUT: api/tickets/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTicket(int id, UpdateTicketDto updateTicketDto)
        {
            // Check if user is admin
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            if (userRole != "Admin")
            {
                return Forbid("Only administrators can update tickets");
            }

            var success = await _ticketService.UpdateTicketAsync(id, updateTicketDto);
            if (!success)
            {
                return NotFound("Ticket not found");
            }

            return NoContent();
        }

        // POST: api/tickets/{id}/comments
        [HttpPost("{id}/comments")]
        public async Task<IActionResult> AddComment(int id, AddCommentDto addCommentDto)
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userName = User.FindFirst(ClaimTypes.Name)?.Value;
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

            if (!int.TryParse(userIdClaim, out var userId) || string.IsNullOrEmpty(userName))
            {
                return Unauthorized("Invalid user information");
            }

            // Check if user can comment on this ticket
            var ticket = await _ticketService.GetTicketByIdAsync(id);
            if (ticket == null)
            {
                return NotFound("Ticket not found");
            }

            // Users can only comment on their own tickets, admins can comment on all
            if (userRole != "Admin" && ticket.UserId != userId)
            {
                return Forbid("You can only comment on your own tickets");
            }

            // Only admins can add internal comments
            if (addCommentDto.IsInternal && userRole != "Admin")
            {
                addCommentDto.IsInternal = false;
            }

            var success = await _ticketService.AddCommentAsync(id, addCommentDto, userId, userName);
            if (!success)
            {
                return NotFound("Ticket not found");
            }

            return Ok();
        }

        // DELETE: api/tickets/comments/{commentId}
        [HttpDelete("comments/{commentId}")]
        public async Task<IActionResult> DeleteComment(int commentId)
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

            if (!int.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized("Invalid user information");
            }

            var success = await _ticketService.DeleteCommentAsync(commentId, userId, userRole == "Admin");
            if (!success)
            {
                return NotFound("Comment not found or access denied");
            }

            return NoContent();
        }

        // DELETE: api/tickets/comments/batch
        [HttpDelete("comments/batch")]
        public async Task<IActionResult> BatchDeleteComments([FromBody] BatchDeleteCommentsDto batchDeleteDto)
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

            if (!int.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized("Invalid user information");
            }

            var deletedCount = await _ticketService.BatchDeleteCommentsAsync(batchDeleteDto.CommentIds, userId, userRole == "Admin");

            return Ok(new { deletedCount });
        }

        // DELETE: api/tickets/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTicket(int id)
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

            if (!int.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized("Invalid user information");
            }

            // Check if user can delete this ticket
            var success = await _ticketService.DeleteTicketAsync(id, userId, userRole == "Admin");
            if (!success)
            {
                return NotFound("Ticket not found or access denied");
            }

            return NoContent();
        }
    }
}
