import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/user_main_layout.dart';
import '../config/routes.dart';
import '../models/ticket.dart';
import '../services/ticket_service.dart';
import '../utils/timezone_utils.dart';

class TicketListScreen extends StatefulWidget {
  const TicketListScreen({super.key});

  @override
  State<TicketListScreen> createState() => _TicketListScreenState();
}

class _TicketListScreenState extends State<TicketListScreen> with TickerProviderStateMixin {
  final TicketService _ticketService = TicketService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<Ticket> _tickets = [];
  List<Ticket> _filteredTickets = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _selectedStatus = 'All';
  String _searchQuery = '';
  bool _showBackToTop = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late AnimationController _successAnimationController;

  final List<String> _statusOptions = ['All', 'Pending', 'InProgress', 'Resolved', 'Closed'];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _successAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _loadTickets();
    _searchController.addListener(_onSearchChanged);
    _scrollController.addListener(_onScrollChanged);
  }

  void _onScrollChanged() {
    final showBackToTop = _scrollController.offset > 200;
    if (showBackToTop != _showBackToTop) {
      setState(() {
        _showBackToTop = showBackToTop;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _successAnimationController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterTickets();
    });
  }

  Future<void> _loadTickets() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final tickets = await _ticketService.getUserTicketsWithUnreadStatus();

      if (mounted) {
        setState(() {
          _tickets = tickets;
          _filteredTickets = tickets;
          _isLoading = false;
        });
        _animationController.forward();
        _filterTickets();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _filterTickets() {
    List<Ticket> filtered = _tickets;

    // Filter by status
    if (_selectedStatus != 'All') {
      filtered = filtered.where((ticket) => ticket.status == _selectedStatus).toList();
    }

    // Filter by search query (only ticket number and title)
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((ticket) {
        return ticket.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               ticket.ticketNumber.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    setState(() {
      _filteredTickets = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return UserMainLayout(
      currentRoute: AppRoutes.ticketList,
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.grey[50]!,
                  Colors.grey[100]!,
                ],
              ),
            ),
            child: Column(
              children: [
                _buildCompactSearchAndFilter(),
                Expanded(
                  child: _buildTicketList(),
                ),
              ],
            ),
          ),
          // 返回顶部按钮
          if (_showBackToTop)
            Positioned(
              right: 16,
              bottom: 16,
              child: _buildBackToTopButton(),
            ),
        ],
      ),
    );
  }

  Widget _buildBackToTopButton() {
    return AnimatedOpacity(
      opacity: _showBackToTop ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.blue[600],
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(24),
          child: InkWell(
            onTap: () {
              _scrollController.animateTo(
                0,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
              );
            },
            borderRadius: BorderRadius.circular(24),
            child: Icon(
              Icons.keyboard_arrow_up,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
      color: Colors.white,
      child: Column(
        children: [
          // 搜索栏和创建按钮
          Row(
            children: [
              // 搜索栏
              Expanded(
                child: Container(
                  height: 44,
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[200]!),
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: '搜索工单编号、标题...',
                      hintStyle: TextStyle(color: Colors.grey[500], fontSize: 14),
                      prefixIcon: Icon(Icons.search, color: Colors.grey[500], size: 20),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: Icon(Icons.clear, color: Colors.grey[500], size: 18),
                              onPressed: () {
                                _searchController.clear();
                              },
                            )
                          : null,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    ),
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // 创建工单按钮
              Container(
                height: 44,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue[500]!, Colors.blue[600]!],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      spreadRadius: 0,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  child: InkWell(
                    onTap: () {
                      context.go(AppRoutes.ticketCreate);
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.add, color: Colors.white, size: 20),
                          const SizedBox(width: 6),
                          Text(
                            '创建',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Compact Status Filter Chips
          SizedBox(
            height: 32,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _statusOptions.length,
              itemBuilder: (context, index) {
                final status = _statusOptions[index];
                final isSelected = _selectedStatus == status;
                String displayText = status;
                if (status == 'InProgress') {
                  displayText = '处理中';
                } else if (status == 'Pending') {
                  displayText = '待处理';
                } else if (status == 'Resolved') {
                  displayText = '已解决';
                } else if (status == 'Closed') {
                  displayText = '已关闭';
                } else if (status == 'All') {
                  displayText = '全部';
                }

                return Padding(
                  padding: EdgeInsets.only(right: index == _statusOptions.length - 1 ? 0 : 8),
                  child: FilterChip(
                    label: Text(
                      displayText,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[700],
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedStatus = status;
                        _filterTickets();
                      });
                    },
                    backgroundColor: Colors.grey[100],
                    selectedColor: _getStatusColor(status),
                    checkmarkColor: Colors.white,
                    elevation: 0,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    visualDensity: VisualDensity.compact,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: BorderSide(
                        color: isSelected ? _getStatusColor(status) : Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTicketList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTickets,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_filteredTickets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _selectedStatus == 'All' ? '暂无工单' : '暂无${_selectedStatus}状态的工单',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '点击右上角的 + 按钮创建新工单',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: _loadTickets,
        color: Colors.blue[600],
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          itemCount: _filteredTickets.length,
          itemBuilder: (context, index) {
            final ticket = _filteredTickets[index];
            return AnimatedContainer(
              duration: Duration(milliseconds: 300 + (index * 50)),
              curve: Curves.easeOutBack,
              child: _buildModernTicketCard(ticket, index),
            );
          },
        ),
      ),
    );
  }

  Widget _buildModernTicketCard(Ticket ticket, int index) {
    final priorityColor = _getPriorityColor(ticket.priority);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ticket.hasUnreadComments ? Colors.blue[300]! : Colors.grey[200]!,
          width: ticket.hasUnreadComments ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: ticket.hasUnreadComments
                ? Colors.blue.withOpacity(0.1)
                : Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            child: InkWell(
              onTap: () {
                context.go('/tickets/${ticket.id}');
              },
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                // Header with ticket number and status
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: priorityColor[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: priorityColor[200]!),
                      ),
                      child: Text(
                        ticket.ticketNumber,
                        style: TextStyle(
                          color: priorityColor[700],
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Spacer(),
                    _buildCompactStatusChip(ticket.status),
                  ],
                ),
                const SizedBox(height: 12),

                // Title
                Text(
                  ticket.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),

                // Description (shorter)
                Text(
                  ticket.description.length > 60
                      ? '${ticket.description.substring(0, 60)}...'
                      : ticket.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),

                // Footer section
                Row(
                  children: [
                    _buildCompactPriorityChip(ticket.priority),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate(ticket.createdDate),
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 12,
                      ),
                    ),
                    const Spacer(),
                    if (ticket.assignedToName != null) ...[
                      Icon(
                        Icons.person,
                        size: 14,
                        color: Colors.blue[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        ticket.assignedToName!,
                        style: TextStyle(
                          color: Colors.blue[600],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 14,
                      color: Colors.grey[400],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        ),
        // 未读评论徽章
        if (ticket.hasUnreadComments)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red[600],
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.withOpacity(0.3),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                '${ticket.unreadCommentsCount}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    ),
    );
  }

  MaterialColor _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange[600]!;
      case 'inprogress':
      case 'in progress':
        return Colors.blue[600]!;
      case 'resolved':
        return Colors.green[600]!;
      case 'closed':
        return Colors.grey[600]!;
      default:
        return Colors.grey[600]!;
    }
  }



  Widget _buildCompactStatusChip(String status) {
    final color = _getStatusColor(status);
    String displayText;

    switch (status.toLowerCase()) {
      case 'pending':
        displayText = '待处理';
        break;
      case 'inprogress':
      case 'in progress':
        displayText = '处理中';
        break;
      case 'resolved':
        displayText = '已解决';
        break;
      case 'closed':
        displayText = '已关闭';
        break;
      default:
        displayText = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        displayText,
        style: TextStyle(
          color: color,
          fontSize: 11,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildCompactPriorityChip(String priority) {
    final color = _getPriorityColor(priority);
    String displayText;

    switch (priority.toLowerCase()) {
      case 'high':
        displayText = '高';
        break;
      case 'medium':
        displayText = '中';
        break;
      case 'low':
        displayText = '低';
        break;
      default:
        displayText = priority;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color[300]!),
      ),
      child: Text(
        displayText,
        style: TextStyle(
          color: color[700],
          fontSize: 11,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }



  String _formatDate(DateTime date) {
    return TimezoneUtils.formatRelativeTime(date);
  }
}
