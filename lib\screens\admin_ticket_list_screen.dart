import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/main_layout.dart';
import '../config/routes.dart';
import '../models/ticket.dart';
import '../services/ticket_service.dart';
import '../utils/timezone_utils.dart';

class AdminTicketListScreen extends StatefulWidget {
  const AdminTicketListScreen({super.key});

  @override
  State<AdminTicketListScreen> createState() => _AdminTicketListScreenState();
}

class _AdminTicketListScreenState extends State<AdminTicketListScreen> with TickerProviderStateMixin {
  final TicketService _ticketService = TicketService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<Ticket> _tickets = [];
  List<Ticket> _filteredTickets = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _selectedStatus = 'All';
  String _selectedPriority = 'All';
  String _selectedCategory = 'All';
  String _searchQuery = '';
  bool _showBackToTop = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _statusOptions = ['All', 'Pending', 'InProgress', 'Resolved', 'Closed'];
  final List<String> _priorityOptions = ['All', 'Low', 'Medium', 'High'];
  final List<String> _categoryOptions = ['All', 'Hardware', 'Software', 'Network', 'Account', 'Other'];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadTickets();
    _searchController.addListener(_onSearchChanged);
    _scrollController.addListener(_onScrollChanged);
  }

  void _onScrollChanged() {
    final showBackToTop = _scrollController.offset > 200;
    if (showBackToTop != _showBackToTop) {
      setState(() {
        _showBackToTop = showBackToTop;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterTickets();
    });
  }

  Future<void> _loadTickets() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 使用管理员API获取所有工单
      final tickets = await _ticketService.getAllTickets();
      
      if (mounted) {
        setState(() {
          _tickets = tickets;
          _filteredTickets = tickets;
          _isLoading = false;
        });
        _animationController.forward();
        _filterTickets();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _filterTickets() {
    List<Ticket> filtered = _tickets;

    // Filter by status
    if (_selectedStatus != 'All') {
      filtered = filtered.where((ticket) => ticket.status == _selectedStatus).toList();
    }

    // Filter by priority
    if (_selectedPriority != 'All') {
      filtered = filtered.where((ticket) => ticket.priority == _selectedPriority).toList();
    }

    // Filter by category
    if (_selectedCategory != 'All') {
      filtered = filtered.where((ticket) => ticket.category == _selectedCategory).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((ticket) {
        return ticket.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               ticket.ticketNumber.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               ticket.userName.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    setState(() {
      _filteredTickets = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: MainLayout(
        currentRoute: AppRoutes.adminTicketList,
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.grey[50]!,
                    Colors.grey[100]!,
                  ],
                ),
              ),
              child: Column(
                children: [
                  _buildAdminToolbar(),
                  Expanded(
                    child: _buildTicketList(),
                  ),
                ],
              ),
            ),
            // 返回顶部按钮
            if (_showBackToTop)
              Positioned(
                right: 16,
                bottom: 16,
                child: _buildBackToTopButton(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackToTopButton() {
    return AnimatedOpacity(
      opacity: _showBackToTop ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.blue[600],
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(24),
          child: InkWell(
            onTap: () {
              _scrollController.animateTo(
                0,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
              );
            },
            borderRadius: BorderRadius.circular(24),
            child: Icon(
              Icons.keyboard_arrow_up,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAdminToolbar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
      color: Colors.white,
      child: Column(
        children: [
          // 标题和统计
          Row(
            children: [
              Icon(
                Icons.admin_panel_settings,
                color: Colors.blue[600],
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'IT工单管理',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Text(
                  '共 ${_filteredTickets.length} 个工单',
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 搜索栏
          Container(
            height: 44,
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索工单编号、标题、用户名...',
                hintStyle: TextStyle(color: Colors.grey[500], fontSize: 14),
                prefixIcon: Icon(Icons.search, color: Colors.grey[500], size: 20),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey[500], size: 18),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              ),
              style: const TextStyle(fontSize: 14),
            ),
          ),
          const SizedBox(height: 12),

          // 筛选器
          _buildFilterChips(),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          // 状态筛选
          _buildFilterChipGroup('状态', _statusOptions, _selectedStatus, (value) {
            setState(() {
              _selectedStatus = value;
              _filterTickets();
            });
          }),
          const SizedBox(width: 16),

          // 优先级筛选
          _buildFilterChipGroup('优先级', _priorityOptions, _selectedPriority, (value) {
            setState(() {
              _selectedPriority = value;
              _filterTickets();
            });
          }),
          const SizedBox(width: 16),

          // 分类筛选
          _buildFilterChipGroup('分类', _categoryOptions, _selectedCategory, (value) {
            setState(() {
              _selectedCategory = value;
              _filterTickets();
            });
          }),
        ],
      ),
    );
  }

  Widget _buildFilterChipGroup(String label, List<String> options, String selectedValue, Function(String) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          height: 32,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options[index];
              final isSelected = selectedValue == option;
              String displayText = _getDisplayText(option);

              return Padding(
                padding: EdgeInsets.only(right: index == options.length - 1 ? 0 : 8),
                child: FilterChip(
                  label: Text(
                    displayText,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey[700],
                      fontWeight: FontWeight.w500,
                      fontSize: 11,
                    ),
                  ),
                  selected: isSelected,
                  onSelected: (selected) => onChanged(option),
                  backgroundColor: Colors.grey[100],
                  selectedColor: _getFilterColor(label, option),
                  checkmarkColor: Colors.white,
                  elevation: 0,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(
                      color: isSelected ? _getFilterColor(label, option) : Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  String _getDisplayText(String value) {
    switch (value) {
      case 'All': return '全部';
      case 'Pending': return '待处理';
      case 'InProgress': return '处理中';
      case 'Resolved': return '已解决';
      case 'Closed': return '已关闭';
      case 'Low': return '低';
      case 'Medium': return '中';
      case 'High': return '高';
      case 'Hardware': return '硬件';
      case 'Software': return '软件';
      case 'Network': return '网络';
      case 'Account': return '账户';
      case 'Other': return '其他';
      default: return value;
    }
  }

  Color _getFilterColor(String label, String value) {
    if (label == '状态') {
      return _getStatusColor(value);
    } else if (label == '优先级') {
      return _getPriorityColor(value);
    } else {
      return Colors.blue[600]!;
    }
  }

  Widget _buildTicketList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTickets,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_filteredTickets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '暂无工单',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '当前筛选条件下没有找到工单',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: _loadTickets,
        color: Colors.blue[600],
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          itemCount: _filteredTickets.length,
          itemBuilder: (context, index) {
            final ticket = _filteredTickets[index];
            return AnimatedContainer(
              duration: Duration(milliseconds: 300 + (index * 50)),
              curve: Curves.easeOutBack,
              child: _buildAdminTicketCard(ticket, index),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAdminTicketCard(Ticket ticket, int index) {
    final priorityColor = _getPriorityColor(ticket.priority);
    final statusColor = _getStatusColor(ticket.status);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () {
            context.go('/admin/tickets/${ticket.id}');
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with ticket number, status and priority
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: priorityColor[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: priorityColor[200]!),
                      ),
                      child: Text(
                        ticket.ticketNumber,
                        style: TextStyle(
                          color: priorityColor[700],
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: statusColor[200]!),
                      ),
                      child: Text(
                        _getDisplayText(ticket.status),
                        style: TextStyle(
                          color: statusColor[700],
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Spacer(),
                    _buildPriorityChip(ticket.priority),
                  ],
                ),
                const SizedBox(height: 12),

                // Title
                Text(
                  ticket.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),

                // Description
                Text(
                  ticket.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),

                // User info and metadata
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: 16,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      ticket.userName,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.category,
                      size: 16,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getDisplayText(ticket.category),
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    Text(
                      _formatDateTime(ticket.createdDate),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),

                // Assignment info
                if (ticket.assignedToName != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.assignment_ind,
                        size: 16,
                        color: Colors.green[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '分配给: ${ticket.assignedToName}',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.green[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],

                // Comments count
                if (ticket.comments.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.comment,
                        size: 16,
                        color: Colors.blue[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${ticket.comments.length} 条评论',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.blue[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityChip(String priority) {
    final color = _getPriorityColor(priority);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color[200]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getPriorityIcon(priority),
            size: 12,
            color: color[700],
          ),
          const SizedBox(width: 4),
          Text(
            _getDisplayText(priority),
            style: TextStyle(
              color: color[700],
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getPriorityIcon(String priority) {
    switch (priority) {
      case 'High':
        return Icons.keyboard_arrow_up;
      case 'Medium':
        return Icons.remove;
      case 'Low':
        return Icons.keyboard_arrow_down;
      default:
        return Icons.remove;
    }
  }

  MaterialColor _getPriorityColor(String priority) {
    switch (priority) {
      case 'High':
        return Colors.red;
      case 'Medium':
        return Colors.orange;
      case 'Low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  MaterialColor _getStatusColor(String status) {
    switch (status) {
      case 'Pending':
        return Colors.orange;
      case 'InProgress':
        return Colors.blue;
      case 'Resolved':
        return Colors.green;
      case 'Closed':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return TimezoneUtils.formatRelativeTime(dateTime);
  }
}
